use crate::{
    errors::{AppError, Result},
    repository::{
        base_repository::{CrudRepository, Entity},
        generic_pagination::{PaginatedRepository, PaginatedResponse},
    },
    utils::{ErrorHelper, ValidateRequest},
};
use async_trait::async_trait;
use std::{marker::PhantomData, sync::Arc};
use uuid::Uuid;

/// Generic CRUD service implementation
pub struct GenericCrudService<E, R>
where
    E: Entity,
    R: CrudRepository<E> + PaginatedRepository<E>,
{
    repository: Arc<R>,
    entity_name: &'static str,
    _phantom: PhantomData<E>,
}

impl<E, R> GenericCrudService<E, R>
where
    E: Entity,
    R: CrudRepository<E> + PaginatedRepository<E>,
{
    pub fn new(repository: Arc<R>, entity_name: &'static str) -> Self {
        Self {
            repository,
            entity_name,
            _phantom: PhantomData,
        }
    }

    /// Get entity by ID with proper error handling
    pub async fn get_by_id_or_error(&self, id: &E::Id) -> Result<E>
    where
        E::Id: std::fmt::Display,
    {
        self.repository
            .find_by_id(id)
            .await?
            .ok_or_else(|| ErrorHelper::not_found(self.entity_name, Some(&id.to_string())))
    }

    /// Validate entity exists
    pub async fn validate_exists(&self, id: &E::Id) -> Result<()>
    where
        E::Id: std::fmt::Display,
    {
        if !self.repository.exists(id).await? {
            return Err(ErrorHelper::not_found(self.entity_name, Some(&id.to_string())));
        }
        Ok(())
    }

    /// Create entity with validation
    pub async fn create_with_validation<Req>(&self, request: Req) -> Result<E>
    where
        Req: ValidateRequest + Into<R::CreateParams>,
    {
        request.validate_request()?;
        self.repository.create(request.into()).await
    }

    /// Update entity with validation
    pub async fn update_with_validation<Req>(&self, id: &E::Id, request: Req) -> Result<E>
    where
        Req: ValidateRequest + Into<R::UpdateParams>,
        E::Id: std::fmt::Display,
    {
        request.validate_request()?;
        
        // Validate entity exists
        self.validate_exists(id).await?;
        
        self.repository.update(id, request.into()).await
    }

    /// Delete entity with validation
    pub async fn delete_with_validation(&self, id: &E::Id) -> Result<()>
    where
        E::Id: std::fmt::Display,
    {
        // Validate entity exists
        self.validate_exists(id).await?;
        
        self.repository.delete(id).await
    }

    /// Get paginated results
    pub async fn get_paginated(&self, request: R::PaginationRequest) -> Result<PaginatedResponse<E>>
    where
        R::PaginationRequest: ValidateRequest,
    {
        request.validate_request()?;
        self.repository.get_paginated(request).await
    }
}

/// Trait for services that need unique field validation
#[async_trait]
pub trait UniqueFieldValidator<E: Entity>: Send + Sync {
    /// Check if a field value is unique (excluding a specific ID)
    async fn is_field_unique(
        &self,
        field_name: &str,
        field_value: &str,
        exclude_id: Option<&E::Id>,
    ) -> Result<bool>;

    /// Validate field uniqueness and return error if not unique
    async fn validate_field_unique(
        &self,
        field_name: &str,
        field_value: &str,
        exclude_id: Option<&E::Id>,
        entity_name: &str,
    ) -> Result<()> {
        if !self.is_field_unique(field_name, field_value, exclude_id).await? {
            return Err(ErrorHelper::conflict(entity_name, field_name, field_value));
        }
        Ok(())
    }
}

/// Generic validation service for common patterns
pub struct ValidationService;

impl ValidationService {
    /// Validate required string field
    pub fn validate_required_string(value: &Option<String>, field_name: &str) -> Result<()> {
        match value {
            Some(s) if !s.trim().is_empty() => Ok(()),
            _ => Err(AppError::Validation(format!("{} is required", field_name))),
        }
    }

    /// Validate string length
    pub fn validate_string_length(
        value: &str,
        field_name: &str,
        min: usize,
        max: usize,
    ) -> Result<()> {
        let len = value.trim().len();
        if len < min || len > max {
            return Err(AppError::Validation(format!(
                "{} must be between {} and {} characters",
                field_name, min, max
            )));
        }
        Ok(())
    }

    /// Validate optional string length
    pub fn validate_optional_string_length(
        value: &Option<String>,
        field_name: &str,
        max: usize,
    ) -> Result<()> {
        if let Some(s) = value {
            if s.len() > max {
                return Err(AppError::Validation(format!(
                    "{} must not exceed {} characters",
                    field_name, max
                )));
            }
        }
        Ok(())
    }

    /// Validate UUID format
    pub fn validate_uuid(value: &str, field_name: &str) -> Result<Uuid> {
        Uuid::parse_str(value).map_err(|_| {
            AppError::Validation(format!("{} must be a valid UUID", field_name))
        })
    }

    /// Validate email format (basic)
    pub fn validate_email(email: &str) -> Result<()> {
        if !email.contains('@') || !email.contains('.') {
            return Err(AppError::Validation("Invalid email format".to_string()));
        }
        Ok(())
    }
}

/// Macro to implement basic CRUD service for an entity
#[macro_export]
macro_rules! impl_basic_crud_service {
    ($service:ident, $entity:ty, $repository:ty, $entity_name:expr) => {
        impl $service {
            pub fn new(repository: std::sync::Arc<$repository>) -> Self {
                Self {
                    crud_service: crate::services::generic_service::GenericCrudService::new(
                        repository.clone(),
                        $entity_name,
                    ),
                    repository,
                }
            }

            pub async fn get_by_id(&self, id: &<$entity as crate::repository::base_repository::Entity>::Id) -> crate::errors::Result<$entity>
            where
                <$entity as crate::repository::base_repository::Entity>::Id: std::fmt::Display,
            {
                self.crud_service.get_by_id_or_error(id).await
            }

            pub async fn delete(&self, id: &<$entity as crate::repository::base_repository::Entity>::Id) -> crate::errors::Result<()>
            where
                <$entity as crate::repository::base_repository::Entity>::Id: std::fmt::Display,
            {
                self.crud_service.delete_with_validation(id).await
            }
        }
    };
}

/// Trait for services that support soft delete
#[async_trait]
pub trait SoftDeletable<E: Entity>: Send + Sync {
    /// Soft delete an entity (mark as inactive)
    async fn soft_delete(&self, id: &E::Id) -> Result<E>;
    
    /// Restore a soft-deleted entity
    async fn restore(&self, id: &E::Id) -> Result<E>;
    
    /// Check if entity is soft-deleted
    async fn is_soft_deleted(&self, id: &E::Id) -> Result<bool>;
}

/// Trait for services that support bulk operations
#[async_trait]
pub trait BulkOperations<E: Entity, CreateReq, UpdateReq>: Send + Sync {
    /// Bulk create entities
    async fn bulk_create(&self, requests: Vec<CreateReq>) -> Result<Vec<E>>;

    /// Bulk update entities
    async fn bulk_update(&self, updates: Vec<(E::Id, UpdateReq)>) -> Result<Vec<E>>;

    /// Bulk delete entities
    async fn bulk_delete(&self, ids: Vec<E::Id>) -> Result<()>;
}
