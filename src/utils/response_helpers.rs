// Response helpers are defined in traits/response_builder.rs
// This file exists to satisfy module structure

use crate::response::{created_response, success_response};
use axum::response::IntoResponse;
use serde::Serialize;
use serde_json::json;

/// Generic response helper for consistent API responses
pub struct ResponseHelper;

impl ResponseHelper {
    /// Create standardized success response for entity retrieval
    pub fn entity_retrieved<T: Serialize>(
        base_path: &str,
        id: Option<&str>,
        code: &str,
        message: &str,
        data: T,
    ) -> impl IntoResponse {
        let path = match id {
            Some(id) => format!("{}/{}", base_path, id),
            None => base_path.to_string(),
        };
        success_response(path, code, message, data)
    }

    /// Create standardized created response
    pub fn entity_created<T: Serialize>(
        base_path: &str,
        code: &str,
        message: &str,
        data: T,
    ) -> impl IntoResponse {
        created_response(base_path.to_string(), code, message, data)
    }

    /// Create standardized deleted response
    pub fn entity_deleted(
        base_path: &str,
        id: &str,
        code: &str,
        message: &str,
    ) -> impl IntoResponse {
        let path = format!("{}/{}", base_path, id);
        let data = json!({
            "id": id,
            "deleted": true
        });
        success_response(path, code, message, data)
    }

    /// Create standardized list response with pagination info
    pub fn entity_list<T: Serialize>(
        base_path: &str,
        code: &str,
        message: &str,
        data: T,
        query_params: Option<&str>,
    ) -> impl IntoResponse {
        let path = match query_params {
            Some(params) => format!("{}?{}", base_path, params),
            None => base_path.to_string(),
        };
        success_response(path, code, message, data)
    }
}

/// Constants for consistent response codes and messages
pub mod response_constants {
    // User module
    pub mod user {
        pub const SUCCESS_CREATE: &str = "USER01";
        pub const SUCCESS_GET: &str = "USER02";
        pub const SUCCESS_LIST: &str = "USER03";
        pub const SUCCESS_UPDATE: &str = "USER04";
        pub const SUCCESS_DELETE: &str = "USER05";

        pub const MSG_CREATED: &str = "User created successfully";
        pub const MSG_RETRIEVED: &str = "User retrieved successfully";
        pub const MSG_LISTED: &str = "Users retrieved successfully";
        pub const MSG_UPDATED: &str = "User updated successfully";
        pub const MSG_DELETED: &str = "User deleted successfully";
    }

    // Permission module
    pub mod permission {
        pub const SUCCESS_CREATE: &str = "PERM01";
        pub const SUCCESS_GET: &str = "PERM02";
        pub const SUCCESS_LIST: &str = "PERM03";
        pub const SUCCESS_UPDATE: &str = "PERM04";
        pub const SUCCESS_DELETE: &str = "PERM05";

        pub const MSG_CREATED: &str = "Permission created successfully";
        pub const MSG_RETRIEVED: &str = "Permission retrieved successfully";
        pub const MSG_LISTED: &str = "Permissions retrieved successfully";
        pub const MSG_UPDATED: &str = "Permission updated successfully";
        pub const MSG_DELETED: &str = "Permission deleted successfully";
    }

    // Auth module
    pub mod auth {
        pub const SUCCESS_LOGIN: &str = "AUTH01";
        pub const SUCCESS_REGISTER: &str = "AUTH02";
        pub const SUCCESS_REFRESH: &str = "AUTH03";
        pub const SUCCESS_OAUTH_URL: &str = "AUTH04";

        pub const MSG_LOGIN: &str = "Login successful";
        pub const MSG_REGISTER: &str = "User registered successfully";
        pub const MSG_REFRESH: &str = "Token refreshed successfully";
        pub const MSG_OAUTH_URL: &str = "OAuth URL generated successfully";
    }

    // Role module
    pub mod role {
        pub const SUCCESS_CREATE: &str = "ROLE01";
        pub const SUCCESS_GET: &str = "ROLE02";
        pub const SUCCESS_LIST: &str = "ROLE03";
        pub const SUCCESS_UPDATE: &str = "ROLE04";
        pub const SUCCESS_DELETE: &str = "ROLE05";

        pub const MSG_CREATED: &str = "Role created successfully";
        pub const MSG_RETRIEVED: &str = "Role retrieved successfully";
        pub const MSG_LISTED: &str = "Roles retrieved successfully";
        pub const MSG_UPDATED: &str = "Role updated successfully";
        pub const MSG_DELETED: &str = "Role deleted successfully";
    }

    // Category module
    pub mod category {
        pub const SUCCESS_CREATE: &str = "CAT01";
        pub const SUCCESS_GET: &str = "CAT02";
        pub const SUCCESS_LIST: &str = "CAT03";
        pub const SUCCESS_UPDATE: &str = "CAT04";
        pub const SUCCESS_DELETE: &str = "CAT05";

        pub const MSG_CREATED: &str = "Category created successfully";
        pub const MSG_RETRIEVED: &str = "Category retrieved successfully";
        pub const MSG_LIST_RETRIEVED: &str = "Categories retrieved successfully";
        pub const MSG_UPDATED: &str = "Category updated successfully";
        pub const MSG_DELETED: &str = "Category deleted successfully";
    }
}
