use crate::{
    database::Database,
    errors::{AppError, Result},
};
use async_trait::async_trait;
use diesel::Connection;
use std::fmt::Debug;

#[derive(Clone)]
pub struct AsyncRepository {
    db: Database,
}

impl AsyncRepository {
    pub fn new(db: Database) -> Self {
        Self { db }
    }

    pub async fn execute<F, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
        T: Send + 'static,
    {
        let db = self.db.clone();

        tokio::task::spawn_blocking(move || -> Result<T> {
            let mut conn = db.pool().get()?;
            // Use deref to get underlying connection
            let conn_ref: &mut diesel::PgConnection = &mut conn;
            operation(conn_ref)
        })
        .await
        .map_err(|e| AppError::Internal(anyhow::anyhow!("Database operation failed: {}", e)))?
    }

    pub async fn execute_batch<F, T>(&self, operations: Vec<F>) -> Result<Vec<T>>
    where
        F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
        T: Send + 'static,
    {
        if operations.is_empty() {
            return Ok(Vec::new());
        }

        let db = self.db.clone();

        tokio::task::spawn_blocking(move || -> Result<Vec<T>> {
            let mut conn = db.pool().get()?;
            let conn_ref: &mut diesel::PgConnection = &mut conn;

            // Execute all operations in a single transaction for consistency
            conn_ref.transaction(|conn| {
                let mut results = Vec::with_capacity(operations.len());

                for operation in operations {
                    results.push(operation(conn)?);
                }

                Ok(results)
            })
        })
        .await
        .map_err(|e| AppError::Internal(anyhow::anyhow!("Batch operation failed: {}", e)))?
    }

    pub async fn execute_readonly<F, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
        T: Send + 'static,
    {
        self.execute(operation).await
    }

    pub async fn execute_transaction<F, T>(&self, operation: F) -> Result<T>
    where
        F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
        T: Send + 'static,
    {
        self.db.transaction(operation).await
    }

    pub async fn execute_concurrent_reads<F1, F2, T1, T2>(
        &self,
        op1: F1,
        op2: F2,
    ) -> Result<(T1, T2)>
    where
        F1: FnOnce(&mut diesel::PgConnection) -> Result<T1> + Send + 'static,
        F2: FnOnce(&mut diesel::PgConnection) -> Result<T2> + Send + 'static,
        T1: Send + 'static,
        T2: Send + 'static,
    {
        let (result1, result2) =
            tokio::join!(self.execute_readonly(op1), self.execute_readonly(op2));

        Ok((result1?, result2?))
    }

    /// Health check for repository layer
    pub async fn is_healthy(&self) -> bool {
        self.db.is_healthy().await
    }

    /// Get connection pool statistics for monitoring
    pub fn pool_stats(&self) -> r2d2::State {
        self.db.pool_status()
    }
}

pub async fn execute_db_operation<T, F>(db: Database, operation: F) -> Result<T>
where
    F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
    T: Send + 'static,
{
    let repo = AsyncRepository::new(db);
    repo.execute(operation).await
}

/// Optimized helper for read operations
pub async fn execute_readonly_operation<T, F>(db: Database, operation: F) -> Result<T>
where
    F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
    T: Send + 'static,
{
    let repo = AsyncRepository::new(db);
    repo.execute_readonly(operation).await
}

// ===== GENERIC CRUD TRAITS =====

/// Generic entity trait that all domain models should implement
pub trait Entity: Send + Sync + Clone + Debug {
    type Id: Send + Sync + Clone + Debug;

    fn id(&self) -> &Self::Id;
}

/// Generic create request trait
pub trait CreateRequest: Send + Sync + Debug {
    type Entity: Entity;
    type CreateParams: Send + Sync + Debug;

    fn to_create_params(self) -> Self::CreateParams;
}

/// Generic update request trait
pub trait UpdateRequest: Send + Sync + Debug {
    type Entity: Entity;
    type UpdateParams: Send + Sync + Debug;

    fn to_update_params(self) -> Self::UpdateParams;
}

/// Generic repository trait for basic CRUD operations
#[async_trait]
pub trait CrudRepository<E: Entity>: Send + Sync {
    type CreateParams: Send + Sync + Debug;
    type UpdateParams: Send + Sync + Debug;
    type Filter: Send + Sync + Debug;

    /// Create a new entity
    async fn create(&self, params: Self::CreateParams) -> Result<E>;

    /// Find entity by ID
    async fn find_by_id(&self, id: &E::Id) -> Result<Option<E>>;

    /// Update entity by ID
    async fn update(&self, id: &E::Id, params: Self::UpdateParams) -> Result<E>;

    /// Delete entity by ID
    async fn delete(&self, id: &E::Id) -> Result<()>;

    /// Find all entities with optional filter
    async fn find_all(&self, filter: Option<Self::Filter>) -> Result<Vec<E>>;

    /// Check if entity exists by ID
    async fn exists(&self, id: &E::Id) -> Result<bool> {
        Ok(self.find_by_id(id).await?.is_some())
    }
}

/// Generic service trait for business logic
#[async_trait]
pub trait CrudService<E: Entity>: Send + Sync {
    type CreateRequest: CreateRequest<Entity = E>;
    type UpdateRequest: UpdateRequest<Entity = E>;
    type Filter: Send + Sync + Debug;

    /// Create a new entity with validation
    async fn create(&self, request: Self::CreateRequest) -> Result<E>;

    /// Get entity by ID with business logic
    async fn get_by_id(&self, id: &E::Id) -> Result<E>;

    /// Update entity with validation
    async fn update(&self, id: &E::Id, request: Self::UpdateRequest) -> Result<E>;

    /// Delete entity with business logic
    async fn delete(&self, id: &E::Id) -> Result<()>;

    /// Get all entities with filter
    async fn get_all(&self, filter: Option<Self::Filter>) -> Result<Vec<E>>;
}

/// Transaction helper with better error context
pub async fn execute_transaction<T, F>(db: Database, operation: F) -> Result<T>
where
    F: FnOnce(&mut diesel::PgConnection) -> Result<T> + Send + 'static,
    T: Send + 'static,
{
    let repo = AsyncRepository::new(db);
    repo.execute_transaction(operation).await
}
