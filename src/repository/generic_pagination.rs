use crate::{
    errors::Result,
    utils::pagination::{PaginationMeta, PaginationRequest},
};
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::fmt::Debug;

/// Generic paginated response wrapper
#[derive(Debug, <PERSON><PERSON>, Serialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub pagination: PaginationMeta,
}

impl<T> PaginatedResponse<T> {
    pub fn new(data: Vec<T>, page: i64, limit: i64, total: i64) -> Self {
        Self {
            data,
            pagination: PaginationMeta::new(page, limit, total),
        }
    }
}

/// Generic pagination request with common filters
#[derive(Debug, <PERSON><PERSON>, Deserialize, Serialize)]
pub struct GenericPaginationRequest {
    #[serde(default = "default_page")]
    pub page: i64,
    #[serde(default = "default_limit")]
    pub limit: i64,
    pub search: Option<String>,
    pub is_active: Option<bool>,
}

impl crate::utils::ValidateRequest for GenericPaginationRequest {
    fn validate_request(&self) -> Result<()> {
        self.validate_pagination()
    }
}

impl PaginationRequest for GenericPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }

    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}

fn default_limit() -> i64 {
    10
}

/// Trait for entities that support pagination with search
pub trait Searchable {
    /// Apply search filter to the query - simplified for now
    fn supports_search() -> bool {
        false
    }
}

/// Trait for entities that have an active status
pub trait Activatable {
    /// Apply active status filter to the query - simplified for now
    fn supports_active_filter() -> bool {
        false
    }
}

/// Generic pagination trait for repositories - simplified
#[async_trait]
pub trait PaginatedRepository<E>: Send + Sync
where
    E: Send + Sync + Debug,
{
    type PaginationRequest: PaginationRequest + Send + Sync;

    /// Get paginated results with filters
    async fn get_paginated(
        &self,
        request: Self::PaginationRequest,
    ) -> Result<PaginatedResponse<E>>;
}

/// Helper struct for building paginated queries
pub struct PaginationQueryBuilder;

impl PaginationQueryBuilder {
    /// Execute a paginated query with count - simplified version
    pub async fn execute_paginated_query<E>(
        _repo: &crate::repository::base_repository::AsyncRepository,
        page: i64,
        limit: i64,
        total: i64,
        data: Vec<E>,
    ) -> Result<PaginatedResponse<E>>
    where
        E: Send + 'static,
    {
        Ok(PaginatedResponse::new(data, page, limit, total))
    }
}

/// Simplified macro for pagination support
#[macro_export]
macro_rules! impl_pagination_for_table {
    ($table:ident, $search_fields:expr, $active_field:expr) => {
        impl crate::repository::generic_pagination::Searchable for $table::table {
            fn supports_search() -> bool {
                true
            }
        }

        impl crate::repository::generic_pagination::Activatable for $table::table {
            fn supports_active_filter() -> bool {
                true
            }
        }
    };
}
