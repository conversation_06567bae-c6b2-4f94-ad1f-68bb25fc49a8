// @generated automatically by Diesel CLI.

pub mod sql_types {
    #[derive(diesel::query_builder::QueryId, diesel::sql_types::SqlType)]
    #[diesel(postgres_type(name = "screen_resolution_enum"))]
    pub struct ScreenResolutionEnum;
}

diesel::table! {
    categories (id) {
        id -> Uuid,
        #[max_length = 50]
        name -> Varchar,
        #[max_length = 60]
        slug -> Varchar,
        description -> Nullable<Text>,
        #[max_length = 20]
        category_type -> Varchar,
        is_active -> Bo<PERSON>,
        created_by -> Nullable<Uuid>,
        updated_by -> Nullable<Uuid>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    laptops (id) {
        id -> Uuid,
        category_id -> Uuid,
        #[max_length = 50]
        brand -> Varchar,
        #[max_length = 100]
        model -> Varchar,
        #[max_length = 200]
        full_name -> Varchar,
        #[max_length = 250]
        slug -> Varchar,
        #[max_length = 50]
        sku -> Nullable<Varchar>,
        #[max_length = 20]
        market_region -> Varchar,
        release_date -> Nullable<Date>,
        description -> Nullable<Text>,
        image_urls -> Nullable<Array<Nullable<Text>>>,
        #[max_length = 20]
        status -> Varchar,
        is_featured -> Bool,
        view_count -> Int8,
        created_by -> Uuid,
        updated_by -> Nullable<Uuid>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    permissions (id) {
        id -> Uuid,
        #[max_length = 100]
        name -> Varchar,
        description -> Nullable<Text>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    prices (id) {
        id -> Uuid,
        laptop_id -> Uuid,
        min_price -> Numeric,
        max_price -> Numeric,
        #[max_length = 3]
        currency -> Varchar,
        #[max_length = 100]
        source -> Nullable<Varchar>,
        #[max_length = 20]
        region -> Varchar,
        effective_date -> Date,
        is_current -> Bool,
        created_by -> Uuid,
        updated_by -> Nullable<Uuid>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    role_permissions (role_id, permission_id) {
        role_id -> Uuid,
        permission_id -> Uuid,
    }
}

diesel::table! {
    roles (id) {
        id -> Uuid,
        #[max_length = 50]
        name -> Varchar,
        description -> Nullable<Text>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    use diesel::sql_types::*;
    use super::sql_types::ScreenResolutionEnum;

    specifications (id) {
        id -> Uuid,
        laptop_id -> Uuid,
        #[max_length = 20]
        cpu_brand -> Varchar,
        #[max_length = 100]
        cpu_model -> Varchar,
        ram_size -> Int4,
        #[max_length = 20]
        ram_type -> Nullable<Varchar>,
        ram_slots_total -> Nullable<Int4>,
        ram_slots_available -> Nullable<Int4>,
        ram_max_capacity -> Nullable<Int4>,
        ram_soldered -> Bool,
        #[max_length = 20]
        storage_type -> Varchar,
        storage_capacity -> Int4,
        storage_slots_total -> Nullable<Int4>,
        storage_slots_available -> Nullable<Int4>,
        storage_max_capacity -> Nullable<Int4>,
        #[max_length = 20]
        gpu_type -> Varchar,
        #[max_length = 100]
        gpu_model -> Nullable<Varchar>,
        screen_size -> Numeric,
        screen_resolution -> ScreenResolutionEnum,
        refresh_rate -> Int4,
        weight -> Nullable<Numeric>,
        #[max_length = 50]
        operating_system -> Nullable<Varchar>,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    titles (id) {
        id -> Uuid,
        #[max_length = 100]
        name -> Varchar,
        description -> Nullable<Text>,
        level_required -> Int4,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
    }
}

diesel::table! {
    user_experience_history (id) {
        id -> Uuid,
        user_id -> Uuid,
        experience_gained -> Int8,
        #[max_length = 255]
        source -> Nullable<Varchar>,
        metadata -> Nullable<Jsonb>,
        created_at -> Timestamptz,
    }
}

diesel::table! {
    user_levels (id) {
        id -> Uuid,
        user_id -> Uuid,
        level -> Int4,
        experience -> Int8,
        total_experience -> Int8,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
        current_title_id -> Nullable<Uuid>,
    }
}

diesel::table! {
    user_roles (user_id, role_id) {
        user_id -> Uuid,
        role_id -> Uuid,
    }
}

diesel::table! {
    user_unlocked_titles (user_id, title_id) {
        user_id -> Uuid,
        title_id -> Uuid,
        unlocked_at -> Timestamptz,
    }
}

diesel::table! {
    users (id) {
        id -> Uuid,
        #[max_length = 50]
        username -> Varchar,
        #[max_length = 100]
        fullname -> Varchar,
        #[max_length = 255]
        password_hash -> Nullable<Varchar>,
        #[max_length = 100]
        email -> Varchar,
        #[max_length = 10]
        otp -> Nullable<Varchar>,
        #[max_length = 50]
        oauth_provider -> Nullable<Varchar>,
        #[max_length = 255]
        oauth_provider_id -> Nullable<Varchar>,
        #[max_length = 255]
        avatar_url -> Nullable<Varchar>,
        email_verified -> Bool,
        created_at -> Timestamptz,
        updated_at -> Timestamptz,
        permission_version -> Int4,
    }
}

diesel::joinable!(laptops -> categories (category_id));
diesel::joinable!(prices -> laptops (laptop_id));
diesel::joinable!(role_permissions -> permissions (permission_id));
diesel::joinable!(role_permissions -> roles (role_id));
diesel::joinable!(specifications -> laptops (laptop_id));
diesel::joinable!(user_experience_history -> users (user_id));
diesel::joinable!(user_levels -> titles (current_title_id));
diesel::joinable!(user_levels -> users (user_id));
diesel::joinable!(user_roles -> roles (role_id));
diesel::joinable!(user_roles -> users (user_id));
diesel::joinable!(user_unlocked_titles -> titles (title_id));
diesel::joinable!(user_unlocked_titles -> users (user_id));

diesel::allow_tables_to_appear_in_same_query!(
    categories,
    laptops,
    permissions,
    prices,
    role_permissions,
    roles,
    specifications,
    titles,
    user_experience_history,
    user_levels,
    user_roles,
    user_unlocked_titles,
    users,
);
