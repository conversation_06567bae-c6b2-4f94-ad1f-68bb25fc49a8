/// Email utility functions for common operations
///
/// Extract user name from email address
/// Falls back to "User" if extraction fails
pub fn extract_user_name_from_email(email: &str) -> String {
    if email.contains('@') {
        email
            .split('@')
            .next()
            .unwrap_or("User")
            .to_string()
    } else {
        "User".to_string()
    }
}

/// Validate email format using basic regex
pub fn is_valid_email(email: &str) -> bool {
    let email_regex = regex::Regex::new(r"^[^\s@]+@[^\s@]+\.[^\s@]+$")
        .expect("Failed to compile email regex");
    email_regex.is_match(email)
}

/// Sanitize email subject to prevent header injection
pub fn sanitize_email_subject(subject: &str) -> String {
    subject
        .chars()
        .filter(|&c| c != '\r' && c != '\n')
        .collect()
}

/// Generate a consistent greeting based on user name
pub fn generate_user_greeting(name: &str) -> String {
    if name.trim().is_empty() || name == "User" {
        "Hello there".to_string()
    } else {
        format!("Hello {}", name)
    }
}

/// Create a standardized email context builder
pub struct EmailContextBuilder {
    user_name: String,
    user_email: String,
    app_url: String,
    app_name: String,
}

impl EmailContextBuilder {
    pub fn new(user_email: String, app_url: String, app_name: String) -> Self {
        let user_name = extract_user_name_from_email(&user_email);
        Self {
            user_name,
            user_email,
            app_url,
            app_name,
        }
    }
    
    pub fn with_user_name(mut self, name: String) -> Self {
        self.user_name = name;
        self
    }
    
    pub fn build(self) -> crate::modules::email::EmailContext {
        crate::modules::email::EmailContext::new(
            self.user_name,
            self.user_email,
            self.app_url,
            self.app_name,
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_user_name_from_email() {
        assert_eq!(extract_user_name_from_email("<EMAIL>"), "john");
        assert_eq!(extract_user_name_from_email("invalid-email"), "User");
        assert_eq!(extract_user_name_from_email(""), "User");
    }

    #[test]
    fn test_is_valid_email() {
        assert!(is_valid_email("<EMAIL>"));
        assert!(is_valid_email("<EMAIL>"));
        assert!(!is_valid_email("invalid-email"));
        assert!(!is_valid_email("@example.com"));
        assert!(!is_valid_email("test@"));
    }

    #[test]
    fn test_sanitize_email_subject() {
        assert_eq!(sanitize_email_subject("Normal subject"), "Normal subject");
        assert_eq!(sanitize_email_subject("Subject\nwith\rnewlines"), "Subjectwithnewlines");
    }

    #[test]
    fn test_generate_user_greeting() {
        assert_eq!(generate_user_greeting("John"), "Hello John");
        assert_eq!(generate_user_greeting(""), "Hello there");
        assert_eq!(generate_user_greeting("User"), "Hello there");
        assert_eq!(generate_user_greeting("   "), "Hello there");
    }
} 