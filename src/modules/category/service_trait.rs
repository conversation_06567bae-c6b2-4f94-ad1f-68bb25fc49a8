use crate::errors::Result;
use crate::modules::category::models::{
    Category, CategoryPaginationRequest, CategoryWithTracking, CreateCategoryRequest,
    PaginatedCategories, PaginatedCategoriesWithTracking, UpdateCategoryRequest,
};
use async_trait::async_trait;
use uuid::Uuid;

/// Category query operations (ISP: Interface segregation)
#[async_trait]
pub trait CategoryQueryTrait: Send + Sync {
    async fn get_category_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking>;
    async fn get_category_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<CategoryWithTracking>;
    async fn get_all_categories(&self) -> Result<Vec<Category>>;
    async fn get_all_categories_with_tracking(&self) -> Result<Vec<CategoryWithTracking>>;
    async fn get_categories_by_type(&self, category_type: &str) -> Result<Vec<Category>>;
    async fn get_categories_by_type_with_tracking(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>>;
    async fn get_categories_with_pagination(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategories>;
    async fn get_categories_with_pagination_and_tracking(
        &self,
        pagination: CategoryPaginationRequest,
    ) -> Result<PaginatedCategoriesWithTracking>;
}

/// Category management operations (ISP: Interface segregation)
#[async_trait]
pub trait CategoryManagementTrait: Send + Sync {
    async fn create_category(&self, request: CreateCategoryRequest, created_by: &Uuid) -> Result<CategoryWithTracking>;
    async fn update_category(&self, id: &Uuid, request: UpdateCategoryRequest, updated_by: &Uuid) -> Result<CategoryWithTracking>;
    async fn delete_category(&self, id: &Uuid) -> Result<()>;
    async fn toggle_category_status(&self, id: &Uuid, updated_by: &Uuid) -> Result<CategoryWithTracking>;
}

/// Category validation operations (ISP: Interface segregation)
#[async_trait]
pub trait CategoryValidationTrait: Send + Sync {
    async fn validate_category_name_unique(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<()>;
    async fn validate_category_slug_unique(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<()>;
    async fn validate_category_exists(&self, id: &Uuid) -> Result<()>;
}

/// Combined category service trait (LSP: Liskov substitution principle)
pub trait CategoryServiceTrait:
    CategoryQueryTrait + CategoryManagementTrait + CategoryValidationTrait + Send + Sync
{
}

// Blanket implementation for any type that implements all the sub-traits
impl<T> CategoryServiceTrait for T where
    T: CategoryQueryTrait + CategoryManagementTrait + CategoryValidationTrait + Send + Sync
{
}
