use crate::{
    errors::Result,
    modules::category::models::{
        Category, CategoryPaginationRequest, CategoryWithTracking, CreateCategoryRequest,
        UpdateCategoryRequest,
    },
    repository::{
        base_repository::{CrudRepository, Entity},
        generic_pagination::{PaginatedRepository, PaginatedResponse},
    },
    services::generic_service::{GenericCrudService, UniqueFieldValidator},
    schema::categories,
};
use async_trait::async_trait;
use diesel::prelude::*;
use std::sync::Arc;
use uuid::Uuid;

// ===== IMPLEMENT GENERIC TRAITS FOR CATEGORY =====

impl Entity for Category {
    type Id = Uuid;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

impl Entity for CategoryWithTracking {
    type Id = Uuid;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

// ===== GENERIC CATEGORY REPOSITORY TRAIT =====

#[async_trait]
pub trait GenericCategoryRepository:
    CrudRepository<CategoryWithTracking> + PaginatedRepository<CategoryWithTracking> + Send + Sync
{
    // Category-specific methods
    async fn find_by_name_and_type(&self, name: &str, category_type: &str) -> Result<Option<CategoryWithTracking>>;
    async fn find_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<Option<CategoryWithTracking>>;
    async fn exists_by_name_and_type(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
    async fn exists_by_slug_and_type(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool>;
    async fn get_by_type(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>>;
    async fn toggle_status(&self, id: &Uuid, updated_by: &Uuid) -> Result<CategoryWithTracking>;
}

// ===== GENERIC CATEGORY SERVICE =====

pub struct GenericCategoryService {
    crud_service: GenericCrudService<CategoryWithTracking, dyn GenericCategoryRepository>,
    repository: Arc<dyn GenericCategoryRepository>,
}

impl GenericCategoryService {
    pub fn new(repository: Arc<dyn GenericCategoryRepository>) -> Self {
        Self {
            crud_service: GenericCrudService::new(repository.clone(), "Category"),
            repository,
        }
    }

    /// Create category with validation
    pub async fn create_category(
        &self,
        request: CreateCategoryRequest,
        created_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        // Validate uniqueness
        self.validate_field_unique("name", &request.name, None, "Category").await?;
        
        if let Some(ref slug) = request.slug {
            self.validate_field_unique("slug", slug, None, "Category").await?;
        }

        // Create category with tracking info
        let create_params = CategoryCreateParams {
            name: request.name,
            description: request.description,
            category_type: request.category_type,
            slug: request.slug,
            is_active: request.is_active.unwrap_or(true),
            created_by: *created_by,
            updated_by: *created_by,
        };

        self.repository.create(create_params).await
    }

    /// Update category with validation
    pub async fn update_category(
        &self,
        id: &Uuid,
        request: UpdateCategoryRequest,
        updated_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        // Validate uniqueness for updated fields
        if let Some(ref name) = request.name {
            self.validate_field_unique("name", name, Some(id), "Category").await?;
        }

        let update_params = CategoryUpdateParams {
            name: request.name,
            description: request.description,
            is_active: request.is_active,
            updated_by: *updated_by,
        };

        self.repository.update(id, update_params).await
    }

    /// Get category by ID
    pub async fn get_category_by_id(&self, id: &Uuid) -> Result<CategoryWithTracking> {
        self.crud_service.get_by_id_or_error(id).await
    }

    /// Delete category
    pub async fn delete_category(&self, id: &Uuid) -> Result<()> {
        self.crud_service.delete_with_validation(id).await
    }

    /// Get paginated categories
    pub async fn get_categories_paginated(
        &self,
        request: CategoryPaginationRequest,
    ) -> Result<PaginatedResponse<CategoryWithTracking>> {
        self.crud_service.get_paginated(request).await
    }

    /// Toggle category status
    pub async fn toggle_category_status(
        &self,
        id: &Uuid,
        updated_by: &Uuid,
    ) -> Result<CategoryWithTracking> {
        self.repository.toggle_status(id, updated_by).await
    }

    /// Get categories by type
    pub async fn get_categories_by_type(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>> {
        self.repository.get_by_type(category_type).await
    }
}

#[async_trait]
impl UniqueFieldValidator<CategoryWithTracking> for GenericCategoryService {
    async fn is_field_unique(
        &self,
        field_name: &str,
        field_value: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool> {
        match field_name {
            "name" => {
                // For category, we need to check uniqueness within the same type
                // This is a simplified version - in real implementation, you'd need the category_type
                Ok(!self.repository.exists_by_name_and_type(field_value, "default", exclude_id).await?)
            }
            "slug" => {
                Ok(!self.repository.exists_by_slug_and_type(field_value, "default", exclude_id).await?)
            }
            _ => Ok(true),
        }
    }
}

// ===== PARAMETER STRUCTS =====

#[derive(Debug, Clone)]
pub struct CategoryCreateParams {
    pub name: String,
    pub description: Option<String>,
    pub category_type: String,
    pub slug: Option<String>,
    pub is_active: bool,
    pub created_by: Uuid,
    pub updated_by: Uuid,
}

#[derive(Debug, Clone)]
pub struct CategoryUpdateParams {
    pub name: Option<String>,
    pub description: Option<Option<String>>,
    pub is_active: Option<bool>,
    pub updated_by: Uuid,
}

// ===== IMPLEMENT PAGINATION FOR CATEGORIES =====

// This would use the macro we created earlier
crate::impl_pagination_for_table!(
    categories,
    [name, description], // Search fields
    is_active // Active field
);

// ===== EXAMPLE REPOSITORY IMPLEMENTATION =====

pub struct GenericDieselCategoryRepository {
    base: crate::repository::base_repository::AsyncRepository,
}

impl GenericDieselCategoryRepository {
    pub fn new(db: crate::database::Database) -> Self {
        Self {
            base: crate::repository::base_repository::AsyncRepository::new(db),
        }
    }
}

#[async_trait]
impl CrudRepository<CategoryWithTracking> for GenericDieselCategoryRepository {
    type CreateParams = CategoryCreateParams;
    type UpdateParams = CategoryUpdateParams;
    type Filter = String; // Category type filter

    async fn create(&self, params: Self::CreateParams) -> Result<CategoryWithTracking> {
        // Implementation would go here
        // This is just a structure example
        todo!("Implement create method")
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<CategoryWithTracking>> {
        // Implementation would go here
        todo!("Implement find_by_id method")
    }

    async fn update(&self, id: &Uuid, params: Self::UpdateParams) -> Result<CategoryWithTracking> {
        // Implementation would go here
        todo!("Implement update method")
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        // Implementation would go here
        todo!("Implement delete method")
    }

    async fn find_all(&self, filter: Option<Self::Filter>) -> Result<Vec<CategoryWithTracking>> {
        // Implementation would go here
        todo!("Implement find_all method")
    }
}

#[async_trait]
impl PaginatedRepository<CategoryWithTracking> for GenericDieselCategoryRepository {
    type PaginationRequest = CategoryPaginationRequest;

    async fn get_paginated(
        &self,
        request: Self::PaginationRequest,
    ) -> Result<PaginatedResponse<CategoryWithTracking>> {
        // Implementation would use the generic pagination helper
        todo!("Implement get_paginated method")
    }
}

#[async_trait]
impl GenericCategoryRepository for GenericDieselCategoryRepository {
    async fn find_by_name_and_type(&self, name: &str, category_type: &str) -> Result<Option<CategoryWithTracking>> {
        todo!("Implement find_by_name_and_type method")
    }

    async fn find_by_slug_and_type(&self, slug: &str, category_type: &str) -> Result<Option<CategoryWithTracking>> {
        todo!("Implement find_by_slug_and_type method")
    }

    async fn exists_by_name_and_type(&self, name: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        todo!("Implement exists_by_name_and_type method")
    }

    async fn exists_by_slug_and_type(&self, slug: &str, category_type: &str, exclude_id: Option<&Uuid>) -> Result<bool> {
        todo!("Implement exists_by_slug_and_type method")
    }

    async fn get_by_type(&self, category_type: &str) -> Result<Vec<CategoryWithTracking>> {
        todo!("Implement get_by_type method")
    }

    async fn toggle_status(&self, id: &Uuid, updated_by: &Uuid) -> Result<CategoryWithTracking> {
        todo!("Implement toggle_status method")
    }
}

// ===== HANDLER IMPLEMENTATION EXAMPLE =====

// Using the generic handler macro
crate::impl_crud_handlers!(
    CategoryCrudHandler,
    CategoryWithTracking,
    GenericCategoryService,
    CreateCategoryRequest,
    UpdateCategoryRequest,
    CategoryPaginationRequest,
    "/api/categories",
    crate::utils::response_helpers::response_constants::category
);

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generic_category_service_structure() {
        // This would be a real test with mock repository
        assert!(true);
    }
}
