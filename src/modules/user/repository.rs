use crate::{
    database::Database,
    errors::{AppError, Result},
    modules::user::models::{
        DieselUser, NewUser, PaginatedUsers, UpdateUser, User, UserPaginationRequest,
    },
    repository::base_repository::AsyncRepository,
    schema::users,
    utils::pagination::PaginationRequest,
};
use async_trait::async_trait;
use diesel::prelude::*;
use std::sync::Arc;
use uuid::Uuid;

/// Parameters for creating a new user (reduces function argument count)
#[derive(Debug, Clone)]
pub struct CreateUserParams {
    pub email: String,
    pub username: String,
    pub fullname: String,
    pub password_hash: Option<String>,
    pub oauth_provider: Option<String>,
    pub oauth_provider_id: Option<String>,
    pub avatar_url: Option<String>,
    pub email_verified: bool,
}

#[async_trait]
pub trait UserRepository: Send + Sync {
    async fn create(&self, params: CreateUserParams) -> Result<User>;
    async fn find_by_id(&self, id: &Uuid) -> Result<Option<User>>;
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    async fn find_by_username(&self, username: &str) -> Result<Option<User>>;
    async fn find_by_email_raw(&self, email: &str) -> Result<Option<DieselUser>>;
    async fn find_all(&self) -> Result<Vec<User>>;
    async fn update(
        &self,
        id: &Uuid,
        email: Option<String>,
        username: Option<String>,
        fullname: Option<String>,
        password_hash: Option<String>,
    ) -> Result<User>;
    async fn delete(&self, id: &Uuid) -> Result<()>;
    async fn find_all_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsers>;
    // Performance optimization methods
    async fn batch_find_by_ids(&self, ids: Vec<Uuid>) -> Result<Vec<User>>;
    async fn exists_by_email(&self, email: &str) -> Result<bool>;
    async fn exists_by_username(&self, username: &str) -> Result<bool>;
    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>>;
}

pub type DynUserRepo = Arc<dyn UserRepository>;

#[derive(Clone)]
pub struct DieselUserRepository {
    repo: AsyncRepository,
}

impl DieselUserRepository {
    pub fn new(db: Database) -> Self {
        Self {
            repo: AsyncRepository::new(db),
        }
    }
}

#[async_trait]
impl UserRepository for DieselUserRepository {
    async fn create(&self, params: CreateUserParams) -> Result<User> {
        self.repo
            .execute_transaction(move |conn| {
                let new_user = NewUser {
                    id: uuid::Uuid::new_v4(),
                    username: params.username.clone(),
                    fullname: params.fullname,
                    password_hash: params.password_hash,
                    email: params.email.clone(),
                    otp: None,
                    oauth_provider: params.oauth_provider,
                    oauth_provider_id: params.oauth_provider_id,
                    avatar_url: params.avatar_url,
                    email_verified: params.email_verified,
                };

                // Attempt to insert directly - let database constraints handle duplicates
                match diesel::insert_into(users::table)
                    .values(&new_user)
                    .execute(conn)
                {
                    Ok(_) => {
                        // Get created user
                        let user = users::table.find(&new_user.id).first::<DieselUser>(conn)?;
                        Ok(user.into())
                    }
                    Err(diesel::result::Error::DatabaseError(
                        diesel::result::DatabaseErrorKind::UniqueViolation,
                        info,
                    )) => {
                        // Handle unique constraint violations gracefully
                        let constraint_name = info.constraint_name().unwrap_or("unknown");
                        if constraint_name.contains("email")
                            || constraint_name.contains("users_email")
                        {
                            Err(AppError::Conflict(format!(
                                "User with email '{}' already exists",
                                params.email
                            )))
                        } else if constraint_name.contains("username")
                            || constraint_name.contains("users_username")
                        {
                            Err(AppError::Conflict(format!(
                                "User with username '{}' already exists",
                                params.username
                            )))
                        } else {
                            Err(AppError::Conflict(
                                "User with these credentials already exists".to_string(),
                            ))
                        }
                    }
                    Err(e) => Err(e.into()),
                }
            })
            .await
    }

    async fn find_by_id(&self, id: &Uuid) -> Result<Option<User>> {
        let id = *id;
        self.repo
            .execute_readonly(move |conn| {
                let user = users::table
                    .find(&id)
                    .first::<DieselUser>(conn)
                    .optional()?;
                Ok(user.map(User::from))
            })
            .await
    }

    async fn find_by_email(&self, email: &str) -> Result<Option<User>> {
        let email = email.to_string();
        self.repo
            .execute_readonly(move |conn| {
                let user = users::table
                    .filter(users::email.eq(&email))
                    .first::<DieselUser>(conn)
                    .optional()?;
                Ok(user.map(User::from))
            })
            .await
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>> {
        let username = username.to_string();
        self.repo
            .execute_readonly(move |conn| {
                let user = users::table
                    .filter(users::username.eq(&username))
                    .first::<DieselUser>(conn)
                    .optional()?;
                Ok(user.map(User::from))
            })
            .await
    }

    async fn find_by_email_raw(&self, email: &str) -> Result<Option<DieselUser>> {
        let email = email.to_string();
        self.repo
            .execute_readonly(move |conn| {
                let user = users::table
                    .filter(users::email.eq(&email))
                    .first::<DieselUser>(conn)
                    .optional()?;
                Ok(user)
            })
            .await
    }

    async fn find_all(&self) -> Result<Vec<User>> {
        self.repo
            .execute_readonly(move |conn| {
                let users_list = users::table
                    .order(users::created_at.desc())
                    .load::<DieselUser>(conn)?;
                Ok(users_list.into_iter().map(User::from).collect())
            })
            .await
    }

    async fn find_all_with_pagination(
        &self,
        pagination: UserPaginationRequest,
    ) -> Result<PaginatedUsers> {
        self.repo
            .execute_readonly(move |conn| {
                // Single query for both count and data using LIMIT/OFFSET
                let total = users::table.count().get_result::<i64>(conn)?;

                let users_list = users::table
                    .order(users::created_at.desc())
                    .limit(pagination.limit())
                    .offset(pagination.offset())
                    .load::<DieselUser>(conn)?;

                let pagination_meta = crate::utils::pagination::PaginationMeta::new(
                    pagination.page(),
                    pagination.limit(),
                    total,
                );

                Ok(PaginatedUsers {
                    data: users_list.into_iter().map(User::from).collect(),
                    pagination: pagination_meta.into(),
                })
            })
            .await
    }

    async fn update(
        &self,
        id: &Uuid,
        email: Option<String>,
        username: Option<String>,
        fullname: Option<String>,
        password_hash: Option<String>,
    ) -> Result<User> {
        let id = *id;
        self.repo
            .execute_transaction(move |conn| {
                // Check if user exists
                let _existing_user = users::table
                    .find(&id)
                    .first::<DieselUser>(conn)
                    .optional()?
                    .ok_or_else(|| AppError::NotFound("User not found".into()))?;

                // Check duplicates if provided
                if let Some(ref new_email) = email {
                    let existing_email = users::table
                        .filter(users::email.eq(new_email))
                        .filter(users::id.ne(&id))
                        .first::<DieselUser>(conn)
                        .optional()?;

                    if existing_email.is_some() {
                        return Err(AppError::Validation("Email already exists".into()));
                    }
                }

                if let Some(ref new_username) = username {
                    let existing_username = users::table
                        .filter(users::username.eq(new_username))
                        .filter(users::id.ne(&id))
                        .first::<DieselUser>(conn)
                        .optional()?;

                    if existing_username.is_some() {
                        return Err(AppError::Validation("Username already exists".into()));
                    }
                }

                let update_data = UpdateUser {
                    email,
                    username,
                    fullname,
                    password_hash: password_hash.map(Some), // Wrap in Some for nullable updates
                    otp: None,
                    updated_at: chrono::Utc::now(),
                    oauth_provider: None,
                    oauth_provider_id: None,
                    avatar_url: None,
                    email_verified: None,
                    permission_version: None,
                };

                diesel::update(users::table.find(&id))
                    .set(&update_data)
                    .execute(conn)?;

                // Get updated user
                let updated_user = users::table.find(&id).first::<DieselUser>(conn)?;

                Ok(updated_user.into())
            })
            .await
    }

    async fn delete(&self, id: &Uuid) -> Result<()> {
        let id = *id;
        self.repo
            .execute(move |conn| {
                let deleted_rows =
                    diesel::delete(users::table.filter(users::id.eq(&id))).execute(conn)?;

                if deleted_rows == 0 {
                    return Err(AppError::NotFound("User not found".into()));
                }

                Ok(())
            })
            .await
    }

    // Performance optimization methods
    async fn batch_find_by_ids(&self, ids: Vec<Uuid>) -> Result<Vec<User>> {
        self.repo
            .execute_readonly(move |conn| {
                let users_list = users::table
                    .filter(users::id.eq_any(&ids))
                    .load::<DieselUser>(conn)?;
                Ok(users_list.into_iter().map(User::from).collect())
            })
            .await
    }

    async fn exists_by_email(&self, email: &str) -> Result<bool> {
        let email = email.to_string();
        self.repo
            .execute_readonly(move |conn| {
                use diesel::dsl::exists;
                let exists = diesel::select(exists(users::table.filter(users::email.eq(&email))))
                    .get_result::<bool>(conn)?;
                Ok(exists)
            })
            .await
    }

    async fn exists_by_username(&self, username: &str) -> Result<bool> {
        let username = username.to_string();
        self.repo
            .execute_readonly(move |conn| {
                use diesel::dsl::exists;
                let exists =
                    diesel::select(exists(users::table.filter(users::username.eq(&username))))
                        .get_result::<bool>(conn)?;
                Ok(exists)
            })
            .await
    }

    async fn get_permission_version(&self, id: &Uuid) -> Result<Option<i32>> {
        let id = *id;
        self.repo
            .execute_readonly(move |conn| {
                let permission_version = users::table
                    .find(&id)
                    .select(users::permission_version)
                    .first::<i32>(conn)
                    .optional()?;
                Ok(permission_version)
            })
            .await
    }
}
