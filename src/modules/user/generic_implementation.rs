use crate::{
    errors::Result,
    modules::user::models::{CreateUserRequest, UpdateUserRequest, User, UserPaginationRequest},
    repository::{
        base_repository::{CreateRequest, Entity, UpdateRequest},
        generic_pagination::{PaginatedRepository, PaginatedResponse},
    },
    services::generic_service::{GenericCrudService, UniqueFieldValidator},
};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

// ===== IMPLEMENT GENERIC TRAITS FOR USER =====

impl Entity for User {
    type Id = Uuid;

    fn id(&self) -> &Self::Id {
        &self.id
    }
}

impl CreateRequest for CreateUserRequest {
    type Entity = User;
    type CreateParams = crate::modules::user::repository::CreateUserParams;

    fn to_create_params(self) -> Self::CreateParams {
        crate::modules::user::repository::CreateUserParams {
            email: self.email,
            username: self.username,
            fullname: self.fullname,
            password_hash: None, // Will be set by service layer
            oauth_provider: None,
            oauth_provider_id: None,
            avatar_url: None,
            email_verified: false,
        }
    }
}

impl UpdateRequest for UpdateUserRequest {
    type Entity = User;
    type UpdateParams = UpdateUserRequest; // Can be the same or create a separate params struct

    fn to_update_params(self) -> Self::UpdateParams {
        self
    }
}

// ===== GENERIC USER SERVICE WRAPPER =====

/// Example of how to wrap the generic service for User-specific operations
pub struct GenericUserService {
    crud_service: GenericCrudService<User, dyn UserRepositoryGeneric>,
    repository: Arc<dyn UserRepositoryGeneric>,
}

/// Extended repository trait that combines CRUD and pagination
#[async_trait]
pub trait UserRepositoryGeneric:
    crate::repository::base_repository::CrudRepository<User>
    + PaginatedRepository<User>
    + Send
    + Sync
{
    // User-specific methods
    async fn find_by_email(&self, email: &str) -> Result<Option<User>>;
    async fn find_by_username(&self, username: &str) -> Result<Option<User>>;
    async fn exists_by_email(&self, email: &str) -> Result<bool>;
    async fn exists_by_username(&self, username: &str) -> Result<bool>;
    async fn exists_by_email_exclude_id(&self, email: &str, exclude_id: &Uuid) -> Result<bool>;
    async fn exists_by_username_exclude_id(&self, username: &str, exclude_id: &Uuid) -> Result<bool>;
}

impl GenericUserService {
    pub fn new(repository: Arc<dyn UserRepositoryGeneric>) -> Self {
        Self {
            crud_service: GenericCrudService::new(repository.clone(), "User"),
            repository,
        }
    }

    /// Create user with password hashing and validation
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<User> {
        // Validate uniqueness
        self.validate_field_unique("email", &request.email, None, "User").await?;
        self.validate_field_unique("username", &request.username, None, "User").await?;

        // Use generic service for basic validation and creation
        // Note: In real implementation, you'd hash the password here
        self.crud_service.create_with_validation(request).await
    }

    /// Update user with validation
    pub async fn update_user(&self, id: &Uuid, request: UpdateUserRequest) -> Result<User> {
        // Validate uniqueness for updated fields
        if let Some(ref email) = request.email {
            self.validate_field_unique("email", email, Some(id), "User").await?;
        }
        if let Some(ref username) = request.username {
            self.validate_field_unique("username", username, Some(id), "User").await?;
        }

        self.crud_service.update_with_validation(id, request).await
    }

    /// Get user by ID
    pub async fn get_user_by_id(&self, id: &Uuid) -> Result<User> {
        self.crud_service.get_by_id_or_error(id).await
    }

    /// Delete user
    pub async fn delete_user(&self, id: &Uuid) -> Result<()> {
        self.crud_service.delete_with_validation(id).await
    }

    /// Get paginated users
    pub async fn get_users_paginated(&self, request: UserPaginationRequest) -> Result<PaginatedResponse<User>> {
        self.crud_service.get_paginated(request).await
    }

    /// Get user by email (user-specific method)
    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        self.repository.find_by_email(email).await
    }

    /// Get user by username (user-specific method)
    pub async fn get_user_by_username(&self, username: &str) -> Result<Option<User>> {
        self.repository.find_by_username(username).await
    }
}

#[async_trait]
impl UniqueFieldValidator<User> for GenericUserService {
    async fn is_field_unique(
        &self,
        field_name: &str,
        field_value: &str,
        exclude_id: Option<&Uuid>,
    ) -> Result<bool> {
        match field_name {
            "email" => match exclude_id {
                Some(id) => Ok(!self.repository.exists_by_email_exclude_id(field_value, id).await?),
                None => Ok(!self.repository.exists_by_email(field_value).await?),
            },
            "username" => match exclude_id {
                Some(id) => Ok(!self.repository.exists_by_username_exclude_id(field_value, id).await?),
                None => Ok(!self.repository.exists_by_username(field_value).await?),
            },
            _ => Ok(true), // Unknown fields are considered unique
        }
    }
}

// ===== EXAMPLE USAGE PATTERNS =====

/// Example of how to implement the generic repository for User
/// This would replace the existing DieselUserRepository
pub struct GenericDieselUserRepository {
    base: crate::repository::base_repository::AsyncRepository,
}

impl GenericDieselUserRepository {
    pub fn new(db: crate::database::Database) -> Self {
        Self {
            base: crate::repository::base_repository::AsyncRepository::new(db),
        }
    }
}

// Implementation would go here - this is just a structure example
// The actual implementation would use the generic patterns we've created

/// Example of how to use the macro for pagination
/// This would be used in the actual repository implementation
#[allow(unused_macros)]
macro_rules! impl_user_pagination {
    () => {
        crate::impl_pagination_for_table!(
            crate::schema::users,
            [name, email, username], // Search fields
            is_active // Active field (if exists)
        );
    };
}

/// Example service factory function
pub fn create_generic_user_service(
    db: crate::database::Database,
) -> GenericUserService {
    let repository = Arc::new(GenericDieselUserRepository::new(db));
    GenericUserService::new(repository)
}

#[cfg(test)]
mod tests {
    use super::*;

    // Example test structure for generic service
    #[tokio::test]
    async fn test_generic_user_service_creation() {
        // This would be a real test with a mock repository
        // Just showing the structure here
        assert!(true);
    }
}
