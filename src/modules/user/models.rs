use crate::schema::users;
use crate::utils::pagination::{PaginationMeta, PaginationRequest};
use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;
use uuid::Uuid;
use validator::Validate;

// ===== DOMAIN MODELS =====
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, ToSchema)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub email: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub oauth_provider: Option<String>,
    pub avatar_url: Option<String>,
    pub email_verified: bool,
    pub permission_version: i32,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, ToSchema)]
#[schema(example = json!({
    "id": "15ef5806-6578-46ca-b999-89c391079e7a",
    "username": "superadmin",
    "fullname": "Super Administrator",
    "email": "<EMAIL>",
    "roles": [
        "super_admin"
    ],
    "created_at": "2025-07-05T01:31:59.286900Z",
    "updated_at": "2025-07-05T01:31:59.286900Z",
    "oauth_provider": null,
    "avatar_url": null,
    "email_verified": true,
    "permission_version": 1
}))]
pub struct UserWithRoles {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub email: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub oauth_provider: Option<String>,
    pub avatar_url: Option<String>,
    pub email_verified: bool,
    pub permission_version: i32,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct CreateUserRequest {
    #[validate(email(message = "Invalid email format"))]
    pub email: String,
    #[validate(length(
        min = 3,
        max = 50,
        message = "Username must be between 3 and 50 characters"
    ))]
    pub username: String,
    #[validate(length(
        min = 2,
        max = 100,
        message = "Full name must be between 2 and 100 characters"
    ))]
    pub fullname: String,
    #[validate(length(min = 6, message = "Password must be at least 6 characters"))]
    pub password: String,
    // Optional role assignment during user creation by role names
    pub role_names: Option<Vec<String>>,
}

#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UpdateUserRequest {
    #[validate(email(message = "Invalid email format"))]
    pub email: Option<String>,
    #[validate(length(
        min = 3,
        max = 50,
        message = "Username must be between 3 and 50 characters"
    ))]
    pub username: Option<String>,
    #[validate(length(
        min = 2,
        max = 100,
        message = "Full name must be between 2 and 100 characters"
    ))]
    pub fullname: Option<String>,
    #[validate(length(min = 6, message = "Password must be at least 6 characters"))]
    pub password: Option<String>,
    // Role updates are handled by a separate endpoint
}

#[derive(Debug, Deserialize, ToSchema)]
pub struct UpdateUserRolesRequest {
    pub role_ids: Vec<Uuid>,
}

// ===== PAGINATION MODELS =====
#[derive(Debug, Deserialize, Validate, ToSchema)]
pub struct UserPaginationRequest {
    #[serde(default = "default_page")]
    #[validate(range(min = 1, message = "Page must be at least 1"))]
    pub page: i64,
    #[serde(default = "default_limit")]
    #[validate(range(min = 1, max = 100, message = "Limit must be between 1 and 100"))]
    pub limit: i64,
}

impl PaginationRequest for UserPaginationRequest {
    fn page(&self) -> i64 {
        self.page
    }
    fn limit(&self) -> i64 {
        self.limit
    }
}

fn default_page() -> i64 {
    1
}
fn default_limit() -> i64 {
    10
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "page": 1,
    "limit": 10,
    "total": 1,
    "total_pages": 1
}))]
pub struct UserPagination {
    pub page: i64,
    pub limit: i64,
    pub total: i64,
    pub total_pages: i64,
}

impl From<PaginationMeta> for UserPagination {
    fn from(meta: PaginationMeta) -> Self {
        Self {
            page: meta.page,
            limit: meta.limit,
            total: meta.total,
            total_pages: meta.total_pages,
        }
    }
}

#[derive(Debug, Serialize, ToSchema)]
pub struct PaginatedUsers {
    pub data: Vec<User>,
    pub pagination: UserPagination,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "users": [
        {
            "id": "15ef5806-6578-46ca-b999-89c391079e7a",
            "username": "superadmin",
            "fullname": "Super Administrator",
            "email": "<EMAIL>",
            "roles": [
                "super_admin"
            ],
            "created_at": "2025-07-05T01:31:59.286900Z",
            "updated_at": "2025-07-05T01:31:59.286900Z"
        }
    ],
    "pagination": {
        "page": 1,
        "limit": 10,
        "total": 1,
        "total_pages": 1
    }
}))]
pub struct PaginatedUsersWithRoles {
    pub users: Vec<UserWithRoles>,
    pub pagination: UserPagination,
}

#[derive(Debug, Serialize, ToSchema)]
#[schema(example = json!({
    "roles": ["super_admin", "member"]
}))]
pub struct UserRolesResponse {
    pub roles: Vec<String>,
}

// ===== DIESEL MODELS =====
#[derive(Debug, Queryable, Selectable)]
#[diesel(table_name = users)]
pub struct DieselUser {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub password_hash: Option<String>, // Nullable for OAuth users
    pub email: String,
    pub otp: Option<String>,
    pub oauth_provider: Option<String>,
    pub oauth_provider_id: Option<String>,
    pub avatar_url: Option<String>,
    pub email_verified: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub permission_version: i32,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = users)]
pub struct NewUser {
    pub id: Uuid,
    pub username: String,
    pub fullname: String,
    pub password_hash: Option<String>, // Nullable for OAuth users
    pub email: String,
    pub otp: Option<String>,
    pub oauth_provider: Option<String>,
    pub oauth_provider_id: Option<String>,
    pub avatar_url: Option<String>,
    pub email_verified: bool,
}

#[derive(Debug, AsChangeset)]
#[diesel(table_name = users)]
pub struct UpdateUser {
    pub username: Option<String>,
    pub fullname: Option<String>,
    pub password_hash: Option<Option<String>>, // For nullable updates
    pub email: Option<String>,
    pub otp: Option<Option<String>>,
    pub updated_at: DateTime<Utc>,
    pub oauth_provider: Option<Option<String>>,
    pub oauth_provider_id: Option<Option<String>>,
    pub avatar_url: Option<Option<String>>,
    pub email_verified: Option<bool>,
    pub permission_version: Option<i32>,
}

// ===== FROM TRAIT IMPLEMENTATIONS =====
impl From<DieselUser> for User {
    fn from(diesel_user: DieselUser) -> Self {
        Self {
            id: diesel_user.id,
            username: diesel_user.username,
            fullname: diesel_user.fullname,
            email: diesel_user.email,
            created_at: diesel_user.created_at,
            updated_at: diesel_user.updated_at,
            oauth_provider: diesel_user.oauth_provider,
            avatar_url: diesel_user.avatar_url,
            email_verified: diesel_user.email_verified,
            permission_version: diesel_user.permission_version,
        }
    }
}

impl UserWithRoles {
    pub fn from_user_and_roles(user: User, roles: Vec<String>) -> Self {
        Self {
            id: user.id,
            username: user.username,
            fullname: user.fullname,
            email: user.email,
            roles,
            created_at: user.created_at,
            updated_at: user.updated_at,
            oauth_provider: user.oauth_provider,
            avatar_url: user.avatar_url,
            email_verified: user.email_verified,
            permission_version: user.permission_version,
        }
    }

    pub fn to_user(&self) -> User {
        User {
            id: self.id,
            username: self.username.clone(),
            fullname: self.fullname.clone(),
            email: self.email.clone(),
            created_at: self.created_at,
            updated_at: self.updated_at,
            oauth_provider: self.oauth_provider.clone(),
            avatar_url: self.avatar_url.clone(),
            email_verified: self.email_verified,
            permission_version: self.permission_version,
        }
    }
}
