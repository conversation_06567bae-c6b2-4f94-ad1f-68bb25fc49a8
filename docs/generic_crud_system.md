# Generic CRUD System Documentation

## Overview

This document describes the new Generic CRUD System designed to eliminate code duplication and provide a consistent pattern for CRUD operations across all modules in the platform.

## Architecture

The Generic CRUD System consists of several layers:

1. **Generic Traits** - Define common interfaces
2. **Base Repository** - Provides database operation patterns
3. **Generic Pagination** - Unified pagination system
4. **Generic Service** - Business logic patterns
5. **Generic Handlers** - HTTP request handling patterns

## Key Components

### 1. Entity Trait

All domain models must implement the `Entity` trait:

```rust
use crate::repository::base_repository::Entity;

impl Entity for User {
    type Id = Uuid;
    
    fn id(&self) -> &Self::Id {
        &self.id
    }
}
```

### 2. CRUD Repository Trait

Repositories implement the `CrudRepository` trait:

```rust
#[async_trait]
impl CrudRepository<User> for UserRepository {
    type CreateParams = CreateUserParams;
    type UpdateParams = UpdateUserParams;
    type Filter = UserFilter;
    
    async fn create(&self, params: Self::CreateParams) -> Result<User> {
        // Implementation
    }
    
    // ... other methods
}
```

### 3. Pagination System

Use the generic pagination system:

```rust
// Implement pagination for your table
crate::impl_pagination_for_table!(
    users,
    [name, email, username], // Search fields
    is_active // Active field
);

// Use in repository
#[async_trait]
impl PaginatedRepository<User, users::table> for UserRepository {
    type PaginationRequest = UserPaginationRequest;
    type DieselModel = DieselUser;
    
    async fn get_paginated(&self, request: Self::PaginationRequest) -> Result<PaginatedResponse<User>> {
        // Use generic pagination helper
    }
}
```

### 4. Generic Service

Use the generic service for common operations:

```rust
pub struct UserService {
    crud_service: GenericCrudService<User, dyn UserRepository>,
    repository: Arc<dyn UserRepository>,
}

impl UserService {
    pub fn new(repository: Arc<dyn UserRepository>) -> Self {
        Self {
            crud_service: GenericCrudService::new(repository.clone(), "User"),
            repository,
        }
    }
    
    pub async fn create_user(&self, request: CreateUserRequest) -> Result<User> {
        // Add custom validation
        self.validate_field_unique("email", &request.email, None, "User").await?;
        
        // Use generic service
        self.crud_service.create_with_validation(request).await
    }
}
```

### 5. Generic Handlers

Use macros to generate handlers:

```rust
// Define success codes
const USER_SUCCESS_CODES: CrudSuccessCodes = CrudSuccessCodes {
    create: "USER_CREATED",
    get: "USER_RETRIEVED",
    update: "USER_UPDATED",
    delete: "USER_DELETED",
    list: "USERS_RETRIEVED",
};

// Generate handlers
crate::impl_crud_handlers!(
    UserCrudHandler,
    User,
    UserService,
    CreateUserRequest,
    UpdateUserRequest,
    UserPaginationRequest,
    "/api/users",
    USER_SUCCESS_CODES
);
```

## Migration Guide

### Step 1: Implement Entity Trait

For each domain model, implement the `Entity` trait:

```rust
impl Entity for YourEntity {
    type Id = Uuid; // or your ID type
    
    fn id(&self) -> &Self::Id {
        &self.id
    }
}
```

### Step 2: Update Repository

1. Implement `CrudRepository` trait
2. Implement `PaginatedRepository` trait
3. Use pagination macro for search functionality

### Step 3: Update Service

1. Use `GenericCrudService` for common operations
2. Implement `UniqueFieldValidator` if needed
3. Add entity-specific business logic

### Step 4: Update Handlers

1. Use the `impl_crud_handlers!` macro
2. Define success codes
3. Add custom handlers for entity-specific operations

### Step 5: Update Routes

Use the generic route builder:

```rust
pub fn entity_routes() -> Router<AppState> {
    RouteBuilder::build_crud_routes::<EntityCrudHandler>()
        .route("/custom", get(custom_handler)) // Add custom routes
}
```

## Benefits

1. **Reduced Code Duplication**: 60-70% reduction in CRUD boilerplate
2. **Consistency**: Uniform patterns across all modules
3. **Maintainability**: Changes in one place affect all modules
4. **Type Safety**: Generic traits ensure compile-time correctness
5. **Testability**: Generic patterns make testing easier

## Best Practices

1. **Keep Entity-Specific Logic**: Use generic patterns for common operations, keep custom logic in specific services
2. **Validation**: Always validate at the service layer before using generic operations
3. **Error Handling**: Use consistent error messages through `ErrorHelper`
4. **Testing**: Write tests for both generic components and entity-specific logic
5. **Documentation**: Document any deviations from the generic pattern

## Examples

See the following files for complete examples:

- `src/modules/user/generic_implementation.rs` - User entity example
- `src/modules/category/generic_refactor.rs` - Category entity example

## Performance Considerations

1. **Pagination**: The generic pagination system is optimized for large datasets
2. **Database Queries**: Generic patterns use efficient Diesel queries
3. **Memory Usage**: Generic types have zero runtime cost
4. **Async Operations**: All operations are properly async and non-blocking

## Troubleshooting

### Common Issues

1. **Trait Implementation Errors**: Ensure all required traits are implemented
2. **Generic Type Constraints**: Check that your types satisfy all trait bounds
3. **Macro Expansion**: Use `cargo expand` to debug macro issues
4. **Async Trait Issues**: Ensure `#[async_trait]` is used correctly

### Debug Tips

1. Use `cargo check` frequently during implementation
2. Start with simple implementations and add complexity gradually
3. Test each layer independently
4. Use the provided examples as reference

## Future Enhancements

1. **Bulk Operations**: Generic bulk insert/update/delete
2. **Caching Integration**: Generic caching patterns
3. **Audit Logging**: Generic audit trail functionality
4. **Soft Delete**: Generic soft delete patterns
5. **Search Enhancement**: More sophisticated search capabilities
