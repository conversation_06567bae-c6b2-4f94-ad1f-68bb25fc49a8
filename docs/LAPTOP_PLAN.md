# Laptop System Integration Plan

## 📋 **Tổng quan**

<PERSON><PERSON><PERSON> hợp hệ thống quản lý thông tin laptop với RBAC hiện tại của platform, bao gồm user tracking, permission-based access control, và multi-level data visibility.

---

## 🎯 **M<PERSON><PERSON> tiêu**

1. **User Tracking**: <PERSON><PERSON><PERSON><PERSON> được ai tạo/cập nhật laptop entries
2. **Permission Control**: <PERSON><PERSON><PERSON> so<PERSON>t quyền truy cập theo role
3. **Public/Private API**: API public cho external access, private cho internal users
4. **Configurable Display**: Dashboard có thể config thông tin hiển thị trong public API
5. **Data Security**: Bảo vệ thông tin nhạy cảm (created_by, sku, etc.)
6. **Scalability**: Dễ mở rộng và bảo trì
7. **Generic Categories**: Thiết kế categories chung cho multiple business domains

## 🏗️ **Architecture Highlights**

### **Generic Categories Design**
- **Scalable**: Một table `categories` chung cho tất cả business domains
- **Flexible**: `category_type` field phân biệt loại ('laptops', 'phones', 'cars', etc.)
- **Future-proof**: Dễ mở rộng cho business domains mới
- **Unique Constraints**: Per category type - cho phép cùng tên nhưng khác domain

### **Data Integrity**
- **Comprehensive Constraints**: 10+ check constraints đảm bảo data quality
- **Foreign Key Relationships**: Proper referential integrity
- **Enum Types**: Type-safe cho screen resolution và status

### **Performance Optimization**
- **30+ Indexes**: Optimized cho search, filter, và pagination
- **Composite Indexes**: Multi-column indexes cho complex queries
- **Category Type Indexes**: Efficient filtering theo business domain

---

## 🗄️ **Database Schema**

### **1. Generic Categories với Multi-Domain Support**
```sql
-- Generic categories table cho tất cả business domains
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(60) NOT NULL,
    description TEXT,
    category_type VARCHAR(20) NOT NULL, -- 'laptops', 'phones', 'cars', etc.
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Unique constraints per category type (allows same name/slug across different types)
    CONSTRAINT unique_name_per_type UNIQUE(name, category_type),
    CONSTRAINT unique_slug_per_type UNIQUE(slug, category_type)
);

-- Category type constraint
ALTER TABLE categories ADD CONSTRAINT check_category_type
    CHECK (category_type IN ('laptops', 'phones', 'cars', 'books', 'electronics'));
```

**🎯 Thiết kế Scalable:**
- Một table `categories` chung cho tất cả business domains
- `category_type` phân biệt loại category ('laptops', 'phones', etc.)
- Unique constraints per category type - cho phép cùng tên nhưng khác domain
- Dễ mở rộng cho business domains mới trong tương lai

### **2. Laptops với Status Management**
```sql
CREATE TABLE laptops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES categories(id), -- References generic categories table
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    slug VARCHAR(250) UNIQUE NOT NULL,
    sku VARCHAR(50) UNIQUE,
    market_region VARCHAR(20) NOT NULL DEFAULT 'Global',
    release_date DATE,
    description TEXT,
    image_urls TEXT[],
    status VARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, published, archived
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    view_count BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Laptop status constraint
ALTER TABLE laptops ADD CONSTRAINT check_laptop_status
    CHECK (status IN ('draft', 'published', 'archived'));
```

**🔗 Integration với Categories:**
- `category_id` references generic `categories` table
- Categories được filter theo `category_type = 'laptops'`
- Flexible design cho future expansion

### **3. Specifications với Expandability**
```sql
-- Enum cho độ phân giải màn hình
CREATE TYPE screen_resolution_enum AS ENUM (
    'FHD', '2K', '2.5K', '3K', '3.5K', '4K'
);

CREATE TABLE specifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,
    
    -- CPU
    cpu_brand VARCHAR(20) NOT NULL,
    cpu_model VARCHAR(100) NOT NULL,
    
    -- RAM với khả năng mở rộng
    ram_size INTEGER NOT NULL, -- GB
    ram_type VARCHAR(20), -- DDR4, DDR5
    ram_slots_total INTEGER, -- Tổng số khe RAM
    ram_slots_available INTEGER, -- Số khe RAM còn trống
    ram_max_capacity INTEGER, -- Dung lượng RAM tối đa có thể nâng cấp
    ram_soldered BOOLEAN NOT NULL DEFAULT FALSE, -- RAM có bị hàn không
    
    -- Storage với khả năng mở rộng
    storage_type VARCHAR(20) NOT NULL, -- SSD, HDD, Hybrid
    storage_capacity INTEGER NOT NULL, -- GB
    storage_slots_total INTEGER, -- Tổng số khe storage (M.2, SATA)
    storage_slots_available INTEGER, -- Số khe storage còn trống
    storage_max_capacity INTEGER, -- Dung lượng storage tối đa
    
    -- GPU
    gpu_type VARCHAR(20) NOT NULL, -- Integrated, Dedicated
    gpu_model VARCHAR(100),
    
    -- Screen với enum và tần số quét
    screen_size DECIMAL(3,1) NOT NULL,
    screen_resolution screen_resolution_enum NOT NULL,
    refresh_rate INTEGER NOT NULL DEFAULT 60, -- Hz
    
    -- Khác
    weight DECIMAL(3,2), -- kg
    operating_system VARCHAR(50),
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Data integrity constraints
ALTER TABLE specifications ADD CONSTRAINT check_gpu_type
    CHECK (gpu_type IN ('Integrated', 'Dedicated'));
ALTER TABLE specifications ADD CONSTRAINT check_storage_type
    CHECK (storage_type IN ('SSD', 'HDD', 'Hybrid'));
ALTER TABLE specifications ADD CONSTRAINT check_ram_type
    CHECK (ram_type IN ('DDR3', 'DDR4', 'DDR5', 'LPDDR4', 'LPDDR5'));
ALTER TABLE specifications ADD CONSTRAINT check_positive_values
    CHECK (ram_size > 0 AND storage_capacity > 0 AND screen_size > 0 AND refresh_rate > 0);
ALTER TABLE specifications ADD CONSTRAINT check_ram_slots
    CHECK (ram_slots_available <= ram_slots_total);
ALTER TABLE specifications ADD CONSTRAINT check_storage_slots
    CHECK (storage_slots_available <= storage_slots_total);
ALTER TABLE specifications ADD CONSTRAINT check_ram_max_capacity
    CHECK (ram_max_capacity >= ram_size);
```

### **4. Prices với Range Support**
```sql
CREATE TABLE prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,
    
    -- Price range thay vì giá cố định
    min_price DECIMAL(12,2) NOT NULL, -- Giá thấp nhất
    max_price DECIMAL(12,2) NOT NULL, -- Giá cao nhất
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    
    -- Metadata
    source VARCHAR(100), -- Nguồn giá (Amazon, Best Buy, v.v.)
    region VARCHAR(20) NOT NULL DEFAULT 'Global',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,
    
    -- User tracking
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    CONSTRAINT check_price_range CHECK (max_price >= min_price)
);
```

---

## 🛡️ **RBAC Integration**

### **1. New Permissions**
```sql
-- Laptop Management Permissions
('laptops:create', 'Create new laptop entries'),
('laptops:read', 'View laptop details and list laptops'),
('laptops:update', 'Update laptop information'),
('laptops:delete', 'Delete laptops'),
('laptops:publish', 'Publish laptops (change status to published)'),
('laptops:feature', 'Mark laptops as featured'),

-- Category Management Permissions  
('categories:create', 'Create new categories'),
('categories:read', 'View category details and list categories'),
('categories:update', 'Update category information'),
('categories:delete', 'Delete categories'),

-- Price Management Permissions
('prices:create', 'Create price entries'),
('prices:read', 'View price information'),
('prices:update', 'Update price information'),
('prices:delete', 'Delete price entries'),

-- Specification Management Permissions
('specs:create', 'Create specification entries'),
('specs:read', 'View specification details'),
('specs:update', 'Update specification information'),
('specs:delete', 'Delete specification entries'),
```

### **2. Updated Role Definitions**

**Super Admin:**
```sql
super_admin: ["admin:all"] -- Có tất cả quyền
```

**Content Manager:**
```sql
content_manager: [
    "laptops:create", "laptops:read", "laptops:update", "laptops:publish", "laptops:feature",
    "categories:create", "categories:read", "categories:update",
    "prices:create", "prices:read", "prices:update",
    "specs:create", "specs:read", "specs:update",
    "profile:read", "profile:update"
]
```

**Content Editor:**
```sql
content_editor: [
    "laptops:create", "laptops:read", "laptops:update",
    "categories:read",
    "prices:create", "prices:read", "prices:update",
    "specs:create", "specs:read", "specs:update",
    "profile:read", "profile:update"
]
```

**Viewer:**
```sql
viewer: [
    "laptops:read", "categories:read", "prices:read", "specs:read",
    "profile:read", "profile:update"
]
```

**Note:** Không có role "guest" - thay vào đó sử dụng API public/private pattern

---

## 🔍 **API Public/Private Pattern**

### **1. Public API (Không cần authentication)**
```rust
pub struct LaptopPublicView {
    pub id: Uuid,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub market_region: String,
    pub image_urls: Vec<String>,
    pub status: String, // chỉ hiển thị nếu = "published"
    pub view_count: i64,
    // Không hiển thị: created_by, updated_by, sku, created_at, updated_at
}
```

**Đặc điểm:**
- Chỉ xem laptops có `status = "published"`
- Thông tin hiển thị có thể được dashboard config
- Không cần authentication
- Dành cho public consumption

### **2. Private API (Cần authentication + permission)**
```rust
pub struct LaptopDetailedView {
    // Tất cả fields của LaptopPublicView +
    pub sku: Option<String>,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub is_featured: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    // Vẫn không hiển thị: created_by, updated_by
}
```

**Đặc điểm:**
- Cần authentication + permission cụ thể
- Xem tất cả laptops (bao gồm draft, archived)
- Xem thông tin chi tiết hơn
- Dành cho internal users có quyền

### **3. Admin/Content Manager (admin:all hoặc full permissions)**
```rust
pub struct LaptopFullView {
    // Tất cả fields của LaptopDetailedView +
    pub created_by: Uuid,
    pub updated_by: Option<Uuid>,
    // Có thể xem tất cả status và thông tin audit
}
```

**Full access:**
- Xem tất cả thông tin bao gồm audit fields
- Thực hiện tất cả operations
- Quản lý status và featured flags

---

## 🌐 **API Endpoints Design**

### **1. Public Endpoints (Không cần authentication)**
```rust
GET /api/public/laptops?page=1&limit=10&status=published
GET /api/public/laptops/{slug}
GET /api/public/categories
GET /api/public/laptops/{id}/specifications
GET /api/public/laptops/{id}/prices
```

**Note:** Thông tin hiển thị trong public API có thể được config qua dashboard

### **2. Private Endpoints (Cần authentication + permission)**
```rust
// Laptop Management
GET /api/private/laptops (laptops:read)
POST /api/private/laptops (laptops:create)
PUT /api/private/laptops/{id} (laptops:update)
DELETE /api/private/laptops/{id} (laptops:delete)
PUT /api/private/laptops/{id}/publish (laptops:publish)
PUT /api/private/laptops/{id}/feature (laptops:feature)

// Category Management
GET /api/private/categories (categories:read)
POST /api/private/categories (categories:create)
PUT /api/private/categories/{id} (categories:update)
DELETE /api/private/categories/{id} (categories:delete)

// Specification Management
GET /api/private/specifications (specs:read)
POST /api/private/specifications (specs:create)
PUT /api/private/specifications/{id} (specs:update)
DELETE /api/private/specifications/{id} (specs:delete)

// Price Management
GET /api/private/prices (prices:read)
POST /api/private/prices (prices:create)
PUT /api/private/prices/{id} (prices:update)
DELETE /api/private/prices/{id} (prices:delete)
```

---

## 🔧 **Implementation Tasks**

### **Phase 1: Database Setup**
- [ ] Create migration files với Diesel
- [ ] Add new permissions to init.rs
- [ ] Update role definitions
- [ ] Create indexes for performance

### **Phase 2: Core Models & Services**
- [ ] Create Rust models cho all tables
- [ ] Implement repository layer
- [ ] Create service layer với permission checks
- [ ] Add user tracking logic

### **Phase 3: API Layer**
- [ ] Create handlers cho public endpoints (không cần auth)
- [ ] Create handlers cho private endpoints (cần auth + permission)
- [ ] Implement permission middleware
- [ ] Add dashboard config cho public API display
- [ ] Add response filtering based on API type (public/private)

### **Phase 4: Testing & Documentation**
- [ ] Unit tests cho services
- [ ] Integration tests cho APIs
- [ ] Update Postman collection
- [ ] API documentation

---

## 🚀 **Migration Strategy**

### **Database Migration**
1. **Clean Slate Approach**: Xóa database và tạo lại với thiết kế mới
2. **Generic Categories**: Migrate từ `laptop_categories` sang `categories` với `category_type = 'laptops'`
3. **Data Integrity**: Comprehensive constraints và foreign keys
4. **Performance**: 30+ indexes được tạo sẵn

### **Implementation Strategy**
1. **Backward Compatibility**: Không ảnh hưởng đến RBAC hiện tại
2. **Incremental Rollout**: Deploy từng module một cách độc lập
3. **Data Validation**: Validate data integrity sau migration
4. **Performance Testing**: Đảm bảo performance không bị ảnh hưởng

### **Migration Files**
- `migrations/2025-07-12-111548_create_complete_laptop_system/up.sql`
- Tạo 4 tables: `categories`, `laptops`, `specifications`, `prices`
- 30+ indexes, 10+ constraints, 4 triggers
- Enum type cho screen resolution

---

## 📊 **Performance Considerations**

### **Database Indexes**
```sql
-- Categories (Generic design với category_type)
CREATE INDEX idx_categories_type ON categories(category_type);
CREATE INDEX idx_categories_active_type ON categories(is_active, category_type);
CREATE INDEX idx_categories_slug_type ON categories(slug, category_type);
CREATE INDEX idx_categories_name_type ON categories(name, category_type);
CREATE INDEX idx_categories_created_by ON categories(created_by);

-- Laptops
CREATE INDEX idx_laptops_slug ON laptops(slug);
CREATE INDEX idx_laptops_category ON laptops(category_id);
CREATE INDEX idx_laptops_brand ON laptops(brand);
CREATE INDEX idx_laptops_status ON laptops(status);
CREATE INDEX idx_laptops_market_region ON laptops(market_region);
CREATE INDEX idx_laptops_created_by ON laptops(created_by);
CREATE INDEX idx_laptops_featured ON laptops(is_featured);
CREATE INDEX idx_laptops_view_count ON laptops(view_count);

-- Specifications (cho filter search)
CREATE INDEX idx_specs_laptop ON specifications(laptop_id);
CREATE INDEX idx_specs_ram_size ON specifications(ram_size);
CREATE INDEX idx_specs_storage_capacity ON specifications(storage_capacity);
CREATE INDEX idx_specs_screen_size ON specifications(screen_size);
CREATE INDEX idx_specs_screen_resolution ON specifications(screen_resolution);
CREATE INDEX idx_specs_cpu_brand ON specifications(cpu_brand);
CREATE INDEX idx_specs_gpu_type ON specifications(gpu_type);
CREATE INDEX idx_specs_storage_type ON specifications(storage_type);

-- Prices
CREATE INDEX idx_prices_laptop_current ON prices(laptop_id, is_current);
CREATE INDEX idx_prices_range ON prices(min_price, max_price);
CREATE INDEX idx_prices_region ON prices(region);
CREATE INDEX idx_prices_created_by ON prices(created_by);
CREATE INDEX idx_prices_effective_date ON prices(effective_date);
CREATE INDEX idx_prices_created_by ON prices(created_by);
```

### **Caching Strategy**
- Redis cache cho popular laptops
- Permission cache (đã có sẵn trong RBAC)
- Category cache cho navigation

---

## 🔒 **Security Considerations**

1. **Input Validation**: Validate tất cả user inputs
2. **SQL Injection Prevention**: Sử dụng Diesel parameterized queries
3. **Access Control**: Permission checks ở mọi layer
4. **Audit Trail**: Track user actions với created_by/updated_by
5. **Rate Limiting**: Giới hạn API calls cho public endpoints

---

## 📈 **Future Enhancements**

1. **Search & Filtering**: Full-text search với Elasticsearch
2. **Image Management**: Upload và resize images
3. **Comparison Feature**: So sánh laptops
4. **Review System**: User reviews với moderation
5. **Analytics**: Track popular laptops, search trends
6. **Multi-language**: Support đa ngôn ngữ
7. **API Versioning**: Version management cho breaking changes 