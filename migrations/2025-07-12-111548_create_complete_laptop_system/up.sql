-- Complete Laptop System with Generic Categories Design
-- Scalable architecture for multiple business domains
-- Includes user tracking, RBAC integration, and performance optimization

-- ===== ENUM TYPES =====

-- Screen resolution enum for specifications
DO $$ BEGIN
    CREATE TYPE screen_resolution_enum AS ENUM (
        'FHD', '2K', '2.5K', '3K', '3.5K', '4K'
    );
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- ===== GENERIC CATEGORIES TABLE =====

CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    slug VARCHAR(60) NOT NULL,
    description TEXT,
    category_type VARCHAR(20) NOT NULL, -- 'laptops', 'phones', 'cars', etc.
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Unique constraints per category type (allows same name/slug across different types)
    CONSTRAINT unique_name_per_type UNIQUE(name, category_type),
    CONSTRAINT unique_slug_per_type UNIQUE(slug, category_type)
);

-- ===== LAPTOPS TABLE =====

CREATE TABLE laptops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    category_id UUID NOT NULL REFERENCES categories(id),
    brand VARCHAR(50) NOT NULL,
    model VARCHAR(100) NOT NULL,
    full_name VARCHAR(200) NOT NULL,
    slug VARCHAR(250) UNIQUE NOT NULL,
    sku VARCHAR(50) UNIQUE,
    market_region VARCHAR(20) NOT NULL DEFAULT 'Global',
    release_date DATE,
    description TEXT,
    image_urls TEXT[],
    status VARCHAR(20) NOT NULL DEFAULT 'draft', -- draft, published, archived
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    view_count BIGINT NOT NULL DEFAULT 0,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== SPECIFICATIONS TABLE =====

CREATE TABLE specifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,

    -- CPU
    cpu_brand VARCHAR(20) NOT NULL,
    cpu_model VARCHAR(100) NOT NULL,

    -- RAM với khả năng mở rộng
    ram_size INTEGER NOT NULL, -- GB
    ram_type VARCHAR(20), -- DDR4, DDR5
    ram_slots_total INTEGER, -- Tổng số khe RAM
    ram_slots_available INTEGER, -- Số khe RAM còn trống
    ram_max_capacity INTEGER, -- Dung lượng RAM tối đa có thể nâng cấp
    ram_soldered BOOLEAN NOT NULL DEFAULT FALSE, -- RAM có bị hàn không

    -- Storage với khả năng mở rộng
    storage_type VARCHAR(20) NOT NULL, -- SSD, HDD, Hybrid
    storage_capacity INTEGER NOT NULL, -- GB
    storage_slots_total INTEGER, -- Tổng số khe storage (M.2, SATA)
    storage_slots_available INTEGER, -- Số khe storage còn trống
    storage_max_capacity INTEGER, -- Dung lượng storage tối đa

    -- GPU
    gpu_type VARCHAR(20) NOT NULL, -- Integrated, Dedicated
    gpu_model VARCHAR(100),

    -- Screen với enum và tần số quét
    screen_size DECIMAL(3,1) NOT NULL,
    screen_resolution screen_resolution_enum NOT NULL,
    refresh_rate INTEGER NOT NULL DEFAULT 60, -- Hz

    -- Khác
    weight DECIMAL(3,2), -- kg
    operating_system VARCHAR(50),

    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- ===== PRICES TABLE =====

CREATE TABLE prices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    laptop_id UUID NOT NULL REFERENCES laptops(id) ON DELETE CASCADE,

    -- Price range thay vì giá cố định
    min_price DECIMAL(12,2) NOT NULL, -- Giá thấp nhất
    max_price DECIMAL(12,2) NOT NULL, -- Giá cao nhất
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',

    -- Metadata
    source VARCHAR(100), -- Nguồn giá (Amazon, Best Buy, v.v.)
    region VARCHAR(20) NOT NULL DEFAULT 'Global',
    effective_date DATE NOT NULL DEFAULT CURRENT_DATE,
    is_current BOOLEAN NOT NULL DEFAULT TRUE,

    -- User tracking
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    CONSTRAINT check_price_range CHECK (max_price >= min_price)
);

-- ===== CONSTRAINTS =====

-- Laptop status constraint
ALTER TABLE laptops ADD CONSTRAINT check_laptop_status
    CHECK (status IN ('draft', 'published', 'archived'));

-- GPU type constraint
ALTER TABLE specifications ADD CONSTRAINT check_gpu_type
    CHECK (gpu_type IN ('Integrated', 'Dedicated'));

-- Storage type constraint
ALTER TABLE specifications ADD CONSTRAINT check_storage_type
    CHECK (storage_type IN ('SSD', 'HDD', 'Hybrid'));

-- RAM type constraint
ALTER TABLE specifications ADD CONSTRAINT check_ram_type
    CHECK (ram_type IN ('DDR3', 'DDR4', 'DDR5', 'LPDDR4', 'LPDDR5'));

-- Positive values constraints
ALTER TABLE specifications ADD CONSTRAINT check_positive_values
    CHECK (ram_size > 0 AND storage_capacity > 0 AND screen_size > 0 AND refresh_rate > 0);

-- RAM slots constraint
ALTER TABLE specifications ADD CONSTRAINT check_ram_slots
    CHECK (ram_slots_available <= ram_slots_total);

-- Storage slots constraint
ALTER TABLE specifications ADD CONSTRAINT check_storage_slots
    CHECK (storage_slots_available <= storage_slots_total);

-- RAM max capacity constraint
ALTER TABLE specifications ADD CONSTRAINT check_ram_max_capacity
    CHECK (ram_max_capacity >= ram_size);

-- Category type constraint (for laptops)
ALTER TABLE categories ADD CONSTRAINT check_category_type
    CHECK (category_type IN ('laptops', 'phones', 'cars', 'books', 'electronics'));

-- ===== INDEXES FOR PERFORMANCE =====

-- Categories indexes
CREATE INDEX idx_categories_type ON categories(category_type);
CREATE INDEX idx_categories_active_type ON categories(is_active, category_type);
CREATE INDEX idx_categories_slug_type ON categories(slug, category_type);
CREATE INDEX idx_categories_name_type ON categories(name, category_type);
CREATE INDEX idx_categories_created_by ON categories(created_by);

-- Laptops indexes
CREATE INDEX idx_laptops_slug ON laptops(slug);
CREATE INDEX idx_laptops_category ON laptops(category_id);
CREATE INDEX idx_laptops_brand ON laptops(brand);
CREATE INDEX idx_laptops_status ON laptops(status);
CREATE INDEX idx_laptops_market_region ON laptops(market_region);
CREATE INDEX idx_laptops_created_by ON laptops(created_by);
CREATE INDEX idx_laptops_featured ON laptops(is_featured);
CREATE INDEX idx_laptops_view_count ON laptops(view_count);

-- Specifications indexes (cho filter search)
CREATE INDEX idx_specs_laptop ON specifications(laptop_id);
CREATE INDEX idx_specs_ram_size ON specifications(ram_size);
CREATE INDEX idx_specs_storage_capacity ON specifications(storage_capacity);
CREATE INDEX idx_specs_screen_size ON specifications(screen_size);
CREATE INDEX idx_specs_screen_resolution ON specifications(screen_resolution);
CREATE INDEX idx_specs_cpu_brand ON specifications(cpu_brand);
CREATE INDEX idx_specs_gpu_type ON specifications(gpu_type);
CREATE INDEX idx_specs_storage_type ON specifications(storage_type);

-- Prices indexes
CREATE INDEX idx_prices_laptop_current ON prices(laptop_id, is_current);
CREATE INDEX idx_prices_range ON prices(min_price, max_price);
CREATE INDEX idx_prices_region ON prices(region);
CREATE INDEX idx_prices_created_by ON prices(created_by);
CREATE INDEX idx_prices_effective_date ON prices(effective_date);

-- ===== TRIGGERS FOR UPDATED_AT =====

-- Categories trigger
CREATE TRIGGER update_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Laptops trigger
CREATE TRIGGER update_laptops_updated_at
    BEFORE UPDATE ON laptops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Specifications trigger
CREATE TRIGGER update_specifications_updated_at
    BEFORE UPDATE ON specifications
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Prices trigger
CREATE TRIGGER update_prices_updated_at
    BEFORE UPDATE ON prices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
