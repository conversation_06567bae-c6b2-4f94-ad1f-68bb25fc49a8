-- Rollback Complete Laptop System
-- Drops all tables, indexes, triggers, and types created in up.sql

-- ===== DROP TRIGGERS =====
DROP TRIGGER IF EXISTS update_prices_updated_at ON prices;
DROP TRIGGER IF EXISTS update_specifications_updated_at ON specifications;
DROP TRIGGER IF EXISTS update_laptops_updated_at ON laptops;
DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;

-- ===== DROP INDEXES =====
-- Prices indexes
DROP INDEX IF EXISTS idx_prices_effective_date;
DROP INDEX IF EXISTS idx_prices_created_by;
DROP INDEX IF EXISTS idx_prices_region;
DROP INDEX IF EXISTS idx_prices_range;
DROP INDEX IF EXISTS idx_prices_laptop_current;

-- Specifications indexes
DROP INDEX IF EXISTS idx_specs_storage_type;
DROP INDEX IF EXISTS idx_specs_gpu_type;
DROP INDEX IF EXISTS idx_specs_cpu_brand;
DROP INDEX IF EXISTS idx_specs_screen_resolution;
DROP INDEX IF EXISTS idx_specs_screen_size;
DROP INDEX IF EXISTS idx_specs_storage_capacity;
DROP INDEX IF EXISTS idx_specs_ram_size;
DROP INDEX IF EXISTS idx_specs_laptop;

-- Laptops indexes
DROP INDEX IF EXISTS idx_laptops_view_count;
DROP INDEX IF EXISTS idx_laptops_featured;
DROP INDEX IF EXISTS idx_laptops_created_by;
DROP INDEX IF EXISTS idx_laptops_market_region;
DROP INDEX IF EXISTS idx_laptops_status;
DROP INDEX IF EXISTS idx_laptops_brand;
DROP INDEX IF EXISTS idx_laptops_category;
DROP INDEX IF EXISTS idx_laptops_slug;

-- Categories indexes
DROP INDEX IF EXISTS idx_categories_created_by;
DROP INDEX IF EXISTS idx_categories_name_type;
DROP INDEX IF EXISTS idx_categories_slug_type;
DROP INDEX IF EXISTS idx_categories_active_type;
DROP INDEX IF EXISTS idx_categories_type;

-- ===== DROP TABLES (in reverse dependency order) =====
DROP TABLE IF EXISTS prices;
DROP TABLE IF EXISTS specifications;
DROP TABLE IF EXISTS laptops;
DROP TABLE IF EXISTS categories;

-- ===== DROP ENUM TYPES =====
DROP TYPE IF EXISTS screen_resolution_enum;
